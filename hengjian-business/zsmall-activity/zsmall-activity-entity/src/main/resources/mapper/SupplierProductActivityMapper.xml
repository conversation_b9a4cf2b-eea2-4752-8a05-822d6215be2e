<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.SupplierProductActivityMapper">

    <!-- ResultMap for ProductActivityStockDTO -->
    <resultMap id="ProductActivityStockDTOMap" type="com.zsmall.activity.entity.domain.dto.productActivity.ProductActivityStockDTO">
        <id column="stock_id" property="id"/>
        <result column="stock_product_sku_stock_id" property="productSkuStockId"/>
        <result column="stock_supplier_activity_code" property="supplierActivityCode"/>
        <result column="stock_supplier_activity_id" property="supplierActivityId"/>
        <result column="stock_distributor_activity_code" property="distributorActivityCode"/>
        <result column="stock_distributor_activity_id" property="distributorActivityId"/>
        <result column="stock_supplier_activity_stock_id" property="supplierActivityStockId"/>
        <result column="stock_warehouse_system_code" property="warehouseSystemCode"/>
        <result column="stock_warehouse_code" property="warehouseCode"/>
        <result column="stock_supported_logistics" property="supportedLogistics"/>
        <result column="stock_quantity_total" property="quantityTotal"/>
        <result column="stock_quantity_sold" property="quantitySold"/>
        <result column="stock_quantity_surplus" property="quantitySurplus"/>
        <result column="stock_del_flag" property="delFlag"/>
        <result column="stock_exception_code" property="exceptionCode"/>
    </resultMap>

    <!-- ResultMap for ProductActivityDetailsDTO -->
    <resultMap id="ProductActivityDetailsDTOMap" type="com.zsmall.activity.entity.domain.dto.productActivity.ProductActivityDetailsDTO">
        <!-- 基本字段映射 -->
        <id column="id" property="id"/>
        <result column="supplier_tenant_id" property="supplierTenantId"/>
        <result column="supplier_activity_id" property="supplierActivityId"/>
        <result column="supplier_activity_code" property="supplierActivityCode"/>
        <result column="distributor_tenant_id" property="distributorTenantId"/>
        <result column="distributor_activity_id" property="distributorActivityId"/>
        <result column="distributor_activity_code" property="distributorActivityCode"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_type" property="activityType"/>
        <result column="activity_state" property="activityState"/>
        <result column="review_state" property="reviewState"/>
        <result column="review_opinion" property="reviewOpinion"/>
        <result column="product_name" property="productName"/>
        <result column="product_img" property="productImg"/>
        <result column="product_code" property="productCode"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="product_sku" property="productSku"/>
        <result column="active_end_time" property="activeEndTime"/>
        <result column="active_start_time" property="activeStartTime"/>
        <result column="site" property="site"/>
        <result column="currency_symbol" property="currencySymbol"/>
        <result column="activity_day" property="activityDay"/>
        <result column="free_storage_period" property="freeStoragePeriod"/>
        <result column="supported_logistics" property="supportedLogistics"/>

        <result column="pickup_quantity_locked" property="pickupQuantityLocked"/>
        <result column="drop_shipping_quantity_locked" property="dropShippingQuantityLocked"/>
        <result column="pickup_locked_used" property="pickupLockedUsed"/>
        <result column="drop_shipping_locked_used" property="dropShippingLockedUsed"/>

        <result column="quantity_minimum" property="quantityMinimum"/>
        <result column="ordered_total" property="orderedTotal"/>
        <result column="deposit_paid_total" property="depositPaidTotal"/>
        <result column="storage_fee_paid_total" property="storageFeePaidTotal"/>
        <result column="supplier_activity_storage_fee" property="supplierActivityStorageFee"/>
        <result column="del_flag" property="delFlag"/>

        <!-- 价格字段映射 - 根据查询类型动态映射供应商或分销商价格 -->
        <result column="activity_unit_price" property="activityUnitPrice"/>
        <result column="activity_operation_fee" property="activityOperationFee"/>
        <result column="activity_final_delivery_fee" property="activityFinalDeliveryFee"/>
        <result column="activity_pick_up_price" property="activityPickUpPrice"/>
        <result column="activity_drop_shipping_price" property="activityDropShippingPrice"/>

        <collection property="productActivityStocks" resultMap="ProductActivityStockDTOMap"/>
    </resultMap>

    <!-- 定义SupplierProductActivityDetails的resultMap -->
    <resultMap id="SupplierProductActivityDetailsMap" type="com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityDetails">
        <!-- 基本字段映射 -->
        <id column="id" property="id"/>
        <result column="supplier_tenant_id" property="supplierTenantId"/>
        <result column="supplier_activity_code" property="supplierActivityCode"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_type" property="activityType"/>
        <result column="activity_state" property="activityState"/>
        <result column="review_state" property="reviewState"/>
        <result column="review_opinion" property="reviewOpinion"/>
        <result column="product_name" property="productName"/>
        <result column="product_img" property="productImg"/>
        <result column="product_code" property="productCode"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="product_sku" property="productSku"/>
        <result column="site" property="site"/>
        <result column="currency_symbol" property="currencySymbol"/>
        <result column="activity_day" property="activityDay"/>
        <result column="free_storage_period" property="freeStoragePeriod"/>
        <result column="pickup_quantity_locked" property="pickupQuantityLocked"/>
        <result column="drop_shipping_quantity_locked" property="dropShippingQuantityLocked"/>
        <result column="pickup_locked_used" property="pickupLockedUsed"/>
        <result column="drop_shipping_locked_used" property="dropShippingLockedUsed"/>
        <result column="quantity_minimum" property="quantityMinimum"/>
        <result column="ordered_total" property="orderedTotal"/>
        <result column="deposit_paid_total" property="depositPaidTotal"/>
        <result column="storage_fee_paid_total" property="storageFeePaidTotal"/>
        <result column="supplier_activity_storage_fee" property="supplierActivityStorageFee"/>
        <result column="exception_code" property="exceptionCode"/>
        <result column="del_flag" property="delFlag"/>
        <result column="supplier_activity_unit_price" property="supplierActivityUnitPrice"/>
        <result column="supplier_activity_operation_fee" property="supplierActivityOperationFee"/>
        <result column="supplier_activity_final_delivery_fee" property="supplierActivityFinalDeliveryFee"/>
        <result column="supplier_activity_pick_up_price" property="supplierActivityPickUpPrice"/>
        <result column="supplier_activity_drop_shipping_price" property="supplierActivityDropShippingPrice"/>

        <!-- 供应商库存集合映射 -->
        <collection property="supplierProductActivityStocks" ofType="com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock">
            <id column="stock_id" property="id"/>
            <id column="product_sku_stock_id" property="productSkuStockId"/>
            <result column="stock_supplier_activity_code" property="supplierActivityCode"/>
            <result column="stock_supplier_activity_id" property="supplierActivityId"/>
            <result column="warehouse_system_code" property="warehouseSystemCode"/>
            <result column="warehouse_code" property="warehouseCode"/>
            <result column="stock_supported_logistics" property="supportedLogistics"/>
            <result column="stock_quantity_total" property="quantityTotal"/>
            <result column="stock_quantity_sold" property="quantitySold"/>
            <result column="stock_quantity_surplus" property="quantitySurplus"/>
            <result column="stock_del_flag" property="delFlag"/>
        </collection>
    </resultMap>

<!--查询供应商活动详情-->
    <select id="selectSupplierActivityDetailsDTO" resultMap="ProductActivityDetailsDTOMap">
        select spa.id,
               spa.supplier_tenant_id,
               spa.id as supplier_activity_id,
               spa.supplier_activity_code,
               null as distributor_activity_id,
               spa.activity_name,
               spa.activity_type,
               spa.activity_state,
               spa.review_state,
               spa.review_opinion,
               spa.product_name,
               spa.product_img,
               spa.product_code,
               spa.product_sku_code,
               spa.product_sku,
               spa.site,
               spa.currency_symbol,
               spa.activity_day,
               spa.free_storage_period,

               spa.pickup_quantity_locked,
               spa.drop_shipping_quantity_locked,
               spa.pickup_locked_used,
               spa.drop_shipping_locked_used,

               spa.quantity_minimum,
               spa.ordered_total,
               spa.deposit_paid_total,
               spa.storage_fee_paid_total,
               spa.supplier_activity_storage_fee,
               spa.del_flag,
               -- 供应商价格信息（统一别名）
               spap.supplier_activity_unit_price as activity_unit_price,
               spap.supplier_activity_operation_fee as activity_operation_fee,
               spap.supplier_activity_final_delivery_fee as activity_final_delivery_fee,
               spap.supplier_activity_pick_up_price as activity_pick_up_price,
               spap.supplier_activity_drop_shipping_price as activity_drop_shipping_price,

               -- 供应商库存信息（映射到ProductActivityStockDTO）
               spas.id as stock_id,
               spas.product_sku_stock_id as stock_product_sku_stock_id,
               spas.supplier_activity_code as stock_supplier_activity_code,
               spas.supplier_activity_id as stock_supplier_activity_id,
               null as stock_distributor_activity_code,
               null as stock_distributor_activity_id,
               null as stock_supplier_activity_stock_id,
               spas.warehouse_system_code as stock_warehouse_system_code,
               spas.warehouse_code as stock_warehouse_code,
               spas.supported_logistics as stock_supported_logistics,
               spas.quantity_total as stock_quantity_total,
               spas.quantity_sold as stock_quantity_sold,
               spas.quantity_surplus as stock_quantity_surplus,
               spas.del_flag as stock_del_flag,
               spas.exception_code as stock_exception_code
        from supplier_product_activity spa
        left join supplier_product_activity_price spap on spa.id = spap.supplier_activity_id and spap.del_flag = 0
        left join supplier_product_activity_stock spas on spa.id = spas.supplier_activity_id and spas.del_flag = 0
        where spa.del_flag = 0
        and spa.supplier_activity_code in
        <foreach collection="activeCode" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
<!--查询分销商活动详情-->
    <select id="selectDistributorActivityDetailsDTO" resultMap="ProductActivityDetailsDTOMap">
        select dpa.id,
               -- 分销商信息
               dpa.distributor_tenant_id,
               dpa.id as distributor_activity_id,
               dpa.distributor_activity_code,
               -- 供应商信息（通过关联获取）
               spa.supplier_tenant_id,
               spa.id as supplier_activity_id,
               spa.supplier_activity_code,
               spa.review_state,
               spa.review_opinion,
               spa.supplier_activity_storage_fee,
               -- 活动基本信息

               dpa.activity_type,
               dpa.activity_state,
               dpa.product_name,
               dpa.product_img,
               dpa.product_code,
               dpa.product_sku_code,
               dpa.product_sku,
               dpa.active_start_time,
               dpa.active_end_time,
               dpa.site,
               dpa.currency_symbol,
               dpa.activity_day,
               dpa.free_storage_period,
               dpa.supported_logistics,

               dpa.pickup_quantity_locked,
               dpa.drop_shipping_quantity_locked,
               dpa.pickup_locked_used,
               dpa.drop_shipping_locked_used,

               dpa.quantity_minimum,
               dpa.ordered_total,
               dpa.deposit_paid_total,
               dpa.storage_fee_paid_total,
               dpa.del_flag,
               -- 分销商价格信息（统一别名）
               dpap.distributor_activity_unit_price as activity_unit_price,
               dpap.distributor_activity_operation_fee as activity_operation_fee,
               dpap.distributor_activity_final_delivery_fee as activity_final_delivery_fee,
               dpap.distributor_activity_pick_up_price as activity_pick_up_price,
               dpap.distributor_activity_drop_shipping_price as activity_drop_shipping_price,
               -- 分销商库存信息（映射到ProductActivityStockDTO）
               dpas.id as stock_id,
               null as stock_product_sku_stock_id,
               null as stock_supplier_activity_code,
               null as stock_supplier_activity_id,
               dpas.distributor_activity_code as stock_distributor_activity_code,
               dpas.distributor_activity_id as stock_distributor_activity_id,
               dpas.supplier_activity_stock_id as stock_supplier_activity_stock_id,
               dpas.warehouse_system_code as stock_warehouse_system_code,
               dpas.warehouse_code as stock_warehouse_code,
               dpas.supported_logistics as stock_supported_logistics,
               dpas.quantity_total as stock_quantity_total,
               dpas.quantity_sold as stock_quantity_sold,
               dpas.quantity_surplus as stock_quantity_surplus,
               dpas.del_flag as stock_del_flag,
               dpas.exception_code as stock_exception_code

        from distributor_product_activity dpa
        left join supplier_product_activity spa on dpa.supplier_activity_id = spa.id and spa.del_flag = 0
        left join distributor_product_activity_price dpap on dpa.id = dpap.distributor_activity_id and dpap.del_flag = 0
        left join distributor_product_activity_stock dpas on dpa.id = dpas.distributor_activity_id and dpas.del_flag = 0
        where dpa.del_flag = 0
        and dpa.distributor_activity_code in
        <foreach collection="activeCode" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectListResponseDTO"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivity">
        select spa.*
        from supplier_product_activity spa
        <if test="@org.apache.commons.lang3.StringUtils@isNotEmpty(bo.distributorTenantId) || @org.apache.commons.lang3.StringUtils@isNotEmpty(bo.distributorActivityCode)">
            inner join distributor_product_activity dpa on dpa.supplier_activity_id = spa.id and dpa.del_flag = 0
        </if>
        where spa.del_flag = 0
        <if test="bo.productSkuCode != null and bo.productSkuCode != ''">
            and spa.product_sku_code like CONCAT('%', #{bo.productSkuCode}, '%')
        </if>
        <if test="bo.productName != null and bo.productName != ''">
            and spa.product_name like CONCAT('%', #{bo.productName}, '%')
        </if>
        <if test="bo.activityName != null and bo.activityName != ''">
            and spa.activity_name like CONCAT('%', #{bo.activityName}, '%')
        </if>
        <if test="bo.productSku != null and bo.productSku != ''">
            and spa.product_sku like CONCAT('%', #{bo.productSku}, '%')
        </if>
        <if test="bo.supplierActivityCode != null and bo.supplierActivityCode != ''">
            and spa.supplier_activity_code like CONCAT('%', #{bo.supplierActivityCode}, '%')
        </if>
        <if test="bo.supplierTenantId != null and bo.supplierTenantId != ''">
            and spa.supplier_tenant_id like concat('%',#{bo.supplierTenantId},'%')
        </if>
        <if test="bo.activityType != null and bo.activityType != ''">
            and spa.activity_type = #{bo.activityType}
        </if>
        <if test="bo.activityState != null and bo.activityState != ''">
            and spa.activity_state = #{bo.activityState}
        </if>
        <if test="bo.productCode != null and bo.productCode != ''">
            and spa.product_code like CONCAT('%', #{bo.productCode}, '%')
        </if>
        <if test="bo.site != null and bo.site != ''">
            and spa.site = #{bo.site}
        </if>
        <if test="bo.currencySymbol != null and bo.currencySymbol != ''">
            and spa.currency_symbol = #{bo.currencySymbol}
        </if>
        <if test="bo.distributorTenantId != null and bo.distributorTenantId != ''">
            and dpa.distributor_tenant_id like concat('%',#{bo.distributorTenantId},'%')
        </if>
        <if test="bo.distributorActivityCode != null and bo.distributorActivityCode != ''">
            and dpa.distributor_activity_code like  concat( '%' , #{bo.distributorActivityCode}, '%' )
        </if>
        <if test="bo.supplierActivityCodes != null and bo.supplierActivityCodes.size() != 0">
            and spa.supplier_activity_code in
            <foreach collection="bo.supplierActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by spa.create_time desc

    </select>

    <select id="selectSupplierProductActivityListExport"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.export.SupplierProductActivityListExportDTO">
        select spa.*
        from supplier_product_activity spa
        left join distributor_product_activity dpa on dpa.supplier_activity_id = spa.id and dpa.del_flag = 0
        where spa.del_flag = 0
        <if test="bo.distributorTenantId != null and bo.distributorTenantId != ''">
            and dpa.distributor_tenant_id=#{bo.distributorTenantId}
        </if>
        <if test="bo.productSkuCode != null and bo.productSkuCode != ''">
            and spa.product_sku_code like CONCAT('%', #{bo.productSkuCode}, '%')
        </if>
        <if test="bo.productName != null and bo.productName != ''">
            and spa.product_name like CONCAT('%', #{bo.productName}, '%')
        </if>
        <if test="bo.activityName != null and bo.activityName != ''">
            and spa.activity_name like CONCAT('%', #{bo.activityName}, '%')
        </if>
        <if test="bo.productSku != null and bo.productSku != ''">
            and spa.product_sku like CONCAT('%', #{bo.productSku}, '%')
        </if>
        <if test="bo.supplierActivityCode != null and bo.supplierActivityCode != ''">
            and spa.supplier_activity_code like CONCAT('%', #{bo.supplierActivityCode}, '%')
        </if>
        <if test="bo.supplierTenantId != null and bo.supplierTenantId != ''">
            and spa.supplier_tenant_id = #{bo.supplierTenantId}
        </if>
        <if test="bo.activityType != null and bo.activityType != ''">
            and spa.activity_type = #{bo.activityType}
        </if>
        <if test="bo.activityState != null and bo.activityState != ''">
            and spa.activity_state = #{bo.activityState}
        </if>
        <if test="bo.productCode != null and bo.productCode != ''">
            and spa.product_code like CONCAT('%', #{bo.productCode}, '%')
        </if>
        <if test="bo.site != null and bo.site != ''">
            and spa.site = #{bo.site}
        </if>
        <if test="bo.currencySymbol != null and bo.currencySymbol != ''">
            and spa.currency_symbol = #{bo.currencySymbol}
        </if>
        <if test="bo.distributorTenantId != null and bo.distributorTenantId != ''">
            and dpa.distributor_tenant_id = #{bo.distributorTenantId}
            and dpa.del_flag = 0
        </if>
        <if test="bo.supplierActivityCodes != null and bo.supplierActivityCodes.size() != 0">
            and spa.supplier_activity_code in
            <foreach collection="bo.supplierActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by spa.create_time desc
    </select>
    <select id="selectAdminProductActivityListExport"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.export.AdminProductActivityListExportDTO">
        select spa.*,
        spap.supplier_activity_unit_price,
        spap.supplier_activity_operation_fee,
        spap.supplier_activity_final_delivery_fee
        from supplier_product_activity spa
        inner join supplier_product_activity_price spap on spa.id = spap.supplier_activity_id and spap.del_flag = 0
        <if test="@org.apache.commons.lang3.StringUtils@isNotEmpty(bo.distributorTenantId) ||
        @org.apache.commons.lang3.StringUtils@isNotEmpty(bo.distributorActivityCode)">
            inner join distributor_product_activity dpa on dpa.supplier_activity_id = spa.id and dpa.del_flag = 0
        </if>
        where spa.del_flag = 0

        <if test="bo.productSkuCode != null and bo.productSkuCode != ''">
            and spa.product_sku_code like CONCAT('%', #{bo.productSkuCode}, '%')
        </if>
        <if test="bo.productName != null and bo.productName != ''">
            and spa.product_name like CONCAT('%', #{bo.productName}, '%')
        </if>
        <if test="bo.activityName != null and bo.activityName != ''">
            and spa.activity_name like CONCAT('%', #{bo.activityName}, '%')
        </if>
        <if test="bo.productSku != null and bo.productSku != ''">
            and spa.product_sku like CONCAT('%', #{bo.productSku}, '%')
        </if>
        <if test="bo.supplierActivityCode != null and bo.supplierActivityCode != ''">
            and spa.supplier_activity_code like CONCAT('%', #{bo.supplierActivityCode}, '%')
        </if>
        <if test="bo.supplierTenantId != null and bo.supplierTenantId != ''">
            and spa.supplier_tenant_id like concat('%',#{bo.supplierTenantId},'%')
        </if>
        <if test="bo.activityType != null and bo.activityType != ''">
            and spa.activity_type = #{bo.activityType}
        </if>
        <if test="bo.activityState != null and bo.activityState != ''">
            and spa.activity_state = #{bo.activityState}
        </if>
        <if test="bo.productCode != null and bo.productCode != ''">
            and spa.product_code like CONCAT('%', #{bo.productCode}, '%')
        </if>
        <if test="bo.site != null and bo.site != ''">
            and spa.site = #{bo.site}
        </if>
        <if test="bo.currencySymbol != null and bo.currencySymbol != ''">
            and spa.currency_symbol = #{bo.currencySymbol}
        </if>
        <if test="bo.distributorTenantId != null and bo.distributorTenantId != ''">
            and dpa.distributor_tenant_id like concat( '%',#{bo.distributorTenantId}, '%' )
        </if>
        <if test="bo.distributorActivityCode != null and bo.distributorActivityCode != ''">
            and dpa.distributor_activity_code like CONCAT('%', #{bo.distributorActivityCode}, '%')
        </if>
        <if test="bo.supplierActivityCodes != null and bo.supplierActivityCodes.size() != 0">
            and spa.supplier_activity_code in
            <foreach collection="bo.supplierActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by spa.create_time desc;
    </select>


    <select id="selectAdminProductActivityDetailsExport"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.export.AdminProductActivityDetailsExportDTO">
        SELECT
            -- 供应商基本信息
            spa.supplier_activity_code,
            spa.supplier_tenant_id,
            spa.activity_type,
            spa.activity_name,
            spa.product_sku_code,
            spa.site,
            spa.currency_symbol,

            -- 供应商锁货数量信息
            spa.pickup_quantity_locked as supplierPickupQuantityLocked,
            spa.drop_shipping_quantity_locked as supplierDropShippingQuantityLocked,
            spa.pickup_locked_used as supplierPickupLockedUsed,
            spa.drop_shipping_locked_used as supplierDropShippingLockedUsed,

            -- 供应商财务信息
            spa.deposit_paid_total,
            spa.storage_fee_paid_total,
            spa.ordered_total as activityOrderNum,

            -- 分销商基本信息
            dpa.distributor_activity_code,
            dpa.distributor_tenant_id,
            dpa.supported_logistics as distributorSupportedLogistics,

            -- 分销商锁货数量信息
            dpa.pickup_quantity_locked as distributorPickupQuantityLocked,
            dpa.drop_shipping_quantity_locked as distributorDropShippingQuantityLocked,
            dpa.pickup_locked_used as distributorPickupLockedUsed,
            dpa.drop_shipping_locked_used as distributorDropShippingLockedUsed,

            -- 分销商锁货总数量（自提+代发）
            COALESCE(dpa.pickup_quantity_locked, 0) + COALESCE(dpa.drop_shipping_quantity_locked, 0) as distributorQuantityLocked,

            -- 分销商财务和订单信息
            dpa.ordered_total as distributorActivityOrderNum,
            dpa.deposit_paid_total as distributorDepositPaidTotal,
            dpa.storage_fee_paid_total as distributorStorageFeePaidTotal,
            dpa.create_time as distributorCreateTime

        FROM supplier_product_activity spa
        LEFT JOIN distributor_product_activity dpa ON dpa.supplier_activity_id = spa.id AND dpa.del_flag = 0
        WHERE spa.del_flag = 0

        <!-- 供应商相关条件 -->
        <if test="bo.supplierTenantId != null and bo.supplierTenantId != ''">
            AND spa.supplier_tenant_id LIKE CONCAT('%', #{bo.supplierTenantId}, '%')
        </if>
        <if test="bo.supplierActivityCode != null and bo.supplierActivityCode != ''">
            AND spa.supplier_activity_code LIKE CONCAT('%', #{bo.supplierActivityCode}, '%')
        </if>
        <if test="bo.activityType != null and bo.activityType != ''">
            AND spa.activity_type = #{bo.activityType}
        </if>
        <if test="bo.activityState != null and bo.activityState != ''">
            AND spa.activity_state = #{bo.activityState}
        </if>
        <if test="bo.activityName != null and bo.activityName != ''">
            AND spa.activity_name LIKE CONCAT('%', #{bo.activityName}, '%')
        </if>
        <if test="bo.productSkuCode != null and bo.productSkuCode != ''">
            AND spa.product_sku_code LIKE CONCAT('%', #{bo.productSkuCode}, '%')
        </if>
        <if test="bo.productName != null and bo.productName != ''">
            AND spa.product_name LIKE CONCAT('%', #{bo.productName}, '%')
        </if>
        <if test="bo.productSku != null and bo.productSku != ''">
            AND spa.product_sku LIKE CONCAT('%', #{bo.productSku}, '%')
        </if>
        <if test="bo.productCode != null and bo.productCode != ''">
            AND spa.product_code LIKE CONCAT('%', #{bo.productCode}, '%')
        </if>
        <if test="bo.site != null and bo.site != ''">
            AND spa.site = #{bo.site}
        </if>
        <if test="bo.currencySymbol != null and bo.currencySymbol != ''">
            AND spa.currency_symbol = #{bo.currencySymbol}
        </if>

        <!-- 分销商相关条件 -->
        <if test="bo.distributorTenantId != null and bo.distributorTenantId != ''">
            AND dpa.distributor_tenant_id LIKE CONCAT('%', #{bo.distributorTenantId}, '%')
        </if>
        <if test="bo.distributorActivityCode != null and bo.distributorActivityCode != ''">
            AND dpa.distributor_activity_code LIKE CONCAT('%', #{bo.distributorActivityCode}, '%')
        </if>

        <!-- 批量供应商活动编码条件 -->
        <if test="bo.supplierActivityCodes != null and bo.supplierActivityCodes.size() != 0">
            AND spa.supplier_activity_code IN
            <foreach collection="bo.supplierActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ORDER BY spa.create_time DESC
    </select>

    <select id="selectProductActivityWarehouseExport"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.export.AdminProductActivityWarehouseExportDTO">
        select spa.supplier_activity_code,
        spa.supplier_tenant_id,
        spa.activity_type,
        spa.activity_name,
        spas.supported_logistics,
        spas.warehouse_code,
        spas.quantity_surplus as quantityLockedRemaining
        from supplier_product_activity spa
        inner join supplier_product_activity_stock spas on spa.id = spas.supplier_activity_id and spas.del_flag = 0
        <if test="@org.apache.commons.lang3.StringUtils@isNotEmpty(bo.distributorTenantId) ||
        @org.apache.commons.lang3.StringUtils@isNotEmpty(bo.distributorActivityCode)">
            inner join distributor_product_activity dpa on dpa.supplier_activity_id = spa.id and dpa.del_flag = 0
        </if>
        where spa.del_flag = 0
        <if test="bo.productSkuCode != null and bo.productSkuCode != ''">
            and spa.product_sku_code like CONCAT('%', #{bo.productSkuCode}, '%')
        </if>
        <if test="bo.productName != null and bo.productName != ''">
            and spa.product_name like CONCAT('%', #{bo.productName}, '%')
        </if>
        <if test="bo.activityName != null and bo.activityName != ''">
            and spa.activity_name like CONCAT('%', #{bo.activityName}, '%')
        </if>
        <if test="bo.productSku != null and bo.productSku != ''">
            and spa.product_sku like CONCAT('%', #{bo.productSku}, '%')
        </if>
        <if test="bo.supplierActivityCode != null and bo.supplierActivityCode != ''">
            and spa.supplier_activity_code like CONCAT('%', #{bo.supplierActivityCode}, '%')
        </if>
        <if test="bo.supplierTenantId != null and bo.supplierTenantId != ''">
            and spa.supplier_tenant_id like concat('%',#{bo.supplierTenantId},'%')
        </if>
        <if test="bo.activityType != null and bo.activityType != ''">
            and spa.activity_type = #{bo.activityType}
        </if>
        <if test="bo.activityState != null and bo.activityState != ''">
            and spa.activity_state = #{bo.activityState}
        </if>
        <if test="bo.productCode != null and bo.productCode != ''">
            and spa.product_code like CONCAT('%', #{bo.productCode}, '%')
        </if>
        <if test="bo.site != null and bo.site != ''">
            and spa.site = #{bo.site}
        </if>
        <if test="bo.currencySymbol != null and bo.currencySymbol != ''">
            and spa.currency_symbol = #{bo.currencySymbol}
        </if>
        <if test="bo.distributorTenantId != null and bo.distributorTenantId != ''">
            and dpa.distributor_tenant_id like concat('%',#{bo.distributorTenantId},'%')
        </if>
        <if test="bo.distributorActivityCode != null and bo.distributorActivityCode != ''">
            and dpa.distributor_activity_code like CONCAT('%', #{bo.distributorActivityCode}, '%')
        </if>
        <if test="bo.supplierActivityCodes != null and bo.supplierActivityCodes.size() != 0">
            and spa.supplier_activity_code in
            <foreach collection="bo.supplierActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by spa.create_time desc
    </select>

    <select id="getProductActivityStockPullDTO"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.ProductActivityStockPullDTO">
        select spa.supplier_activity_code,
            spa.product_sku,
            spas.warehouse_system_code,
            spas.warehouse_code,
            spas.supported_logistics,
            spas.quantity_total,
            spas.quantity_total
        from supplier_product_activity spa
        inner join supplier_product_activity_stock spas on spa.id = spas.supplier_activity_id and spas.del_flag = 0
        where spa.del_flag = 0
    </select>

    <update id="updateSupplierProductActivityException">
        update supplier_product_activity spa
            inner join supplier_product_activity_stock spas on spa.id = spas.supplier_activity_id
        set spa.exception_code = #{i},
        spas.exception_code = #{i}
        where spa.product_sku_code = #{productSkuCode}
        <if test="warehouseSystemCode != null and warehouseSystemCode != ''">
            and spas.warehouse_system_code = #{warehouseSystemCode}
        </if>
    </update>

    <select id="getSupplierAvailableActivesBySpu"
            resultMap="SupplierProductActivityDetailsMap">
        SELECT
            spa.*,
            spap.supplier_activity_unit_price,
            spap.supplier_activity_operation_fee,
            spap.supplier_activity_final_delivery_fee,
            spap.supplier_activity_pick_up_price,
            spap.supplier_activity_drop_shipping_price,
            spas.id as stock_id,
            spas.product_sku_stock_id as product_sku_stock_id,
            spas.supplier_activity_code as stock_supplier_activity_code,
            spas.supplier_activity_id as stock_supplier_activity_id,
            spas.warehouse_system_code,
            spas.warehouse_code,
            spas.supported_logistics as stock_supported_logistics,
            spas.quantity_total as stock_quantity_total,
            spas.quantity_sold as stock_quantity_sold,
            spas.quantity_surplus as stock_quantity_surplus,
            spas.del_flag as stock_del_flag
        FROM supplier_product_activity spa
        INNER JOIN supplier_product_activity_stock spas ON spa.id = spas.supplier_activity_id AND spas.del_flag = 0
        INNER JOIN supplier_product_activity_price spap ON spa.id = spap.supplier_activity_id AND spap.del_flag = 0
        INNER JOIN product_sku ps ON spa.product_sku_code = ps.product_sku_code AND ps.del_flag = 0
        WHERE spa.del_flag = 0
            AND spa.product_code = #{productCode}
            AND spa.site = #{site}
            AND spa.activity_state IN ('InProgress','Published')
            AND spa.exception_code = 0
            AND spas.quantity_surplus > 0
            AND ps.shelf_state = 'OnShelf'
        ORDER BY spa.id DESC
    </select>

    <update id="updateProductActivityStockException">
        update supplier_product_activity_stock spas
        left join distributor_product_activity_stock dpas
            on spas.product_sku_stock_id = dpas.supplier_activity_stock_id
        set spas.exception_code = #{i},
        dpas.exception_code = #{i}
        where spas.product_sku_stock_id = #{id}
    </update>

    <update id="updateProductActivityExceptionByStock">
        update supplier_product_activity spa
        left  join distributor_product_activity dpa on dpa.supplier_activity_id = spa.id
        inner join (
            select supplier_activity_id,
                   case when count(case when exception_code != 0 then 1 end) > 0 then 1 else 0 end as has_exception
            from supplier_product_activity_stock
            where del_flag = 0
            group by supplier_activity_id
        ) stock_status on spa.id = stock_status.supplier_activity_id
        set spa.exception_code = stock_status.has_exception,
            dpa.exception_code = stock_status.has_exception
        where spa.product_sku_code = #{productSkuCode}
        and spa.del_flag = 0
    </update>

    <update id="updateProductActivityExceptionByActivity">
        update supplier_product_activity spa
        left  join distributor_product_activity dpa on dpa.supplier_activity_id = spa.id
        inner join (
        select supplier_activity_id,
        case when count(case when exception_code != 0 then 1 end) > 0 then 1 else 0 end as has_exception
        from supplier_product_activity_stock
        where del_flag = 0
        group by supplier_activity_id
        ) stock_status on spa.id = stock_status.supplier_activity_id
        set spa.exception_code = stock_status.has_exception,
        dpa.exception_code = stock_status.has_exception
        where spa.supplier_activity_code = #{supplierActivityCode}
        and spa.del_flag = 0
    </update>

    <select id="getActivityProductSkuList"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.ActivityProductSkuDTO">
        select p.name                           as productName,
               p.supported_logistics            as supportedLogistics,
               p.product_code,
               ps.product_sku_code,
               ps.id                            as productSkuId,
               ps.sku                           as productSku,
               psp.original_pick_up_price       as pickUpPrice,
               psp.original_drop_shipping_price as dropShippingPrice,
               psp.country_code                   as site,
               psp.currency_symbol              as currencySymbol
        from product p
                 inner join product_sku ps on p.id = ps.product_id and ps.del_flag = 0
                 inner join product_sku_price psp on ps.id = psp.product_sku_id
        where psp.del_flag = 0
        <if test="productName != null and productName != ''">
            and p.name like CONCAT('%', #{productName}, '%')
        </if>
        <if test="productSkuCode != null and productSkuCode != ''">
            and ps.product_sku_code = #{productSkuCode}
        </if>
        <if test="sku != null and sku != ''">
            and ps.sku = #{sku}
        </if>
        and psp.country_code = #{site}
        and psp.original_pick_up_price is not null
        and p.shelf_state = 'OnShelf'
        and ps.shelf_state = 'OnShelf'
        and ps.tenant_id = #{tenantId}
        order by ps.id desc
    </select>
</mapper>
