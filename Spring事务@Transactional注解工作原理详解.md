# Spring事务@Transactional注解工作原理详解

## 概述

`@Transactional` 注解是Spring框架提供的声明式事务管理机制，它基于 **Spring AOP（面向切面编程）** 和 **代理模式** 实现。本文将深入解析为什么加了这个注解就会有事务效果。

## 🔧 核心原理

### 1. AOP代理机制

当Spring容器启动时，会发生以下过程：

```java
// 原始对象
ProductActiveService originalService = new ProductActiveService();

// Spring检测到@Transactional注解，创建代理对象
ProductActiveService proxyService = createTransactionProxy(originalService);

// 注入到容器中的是代理对象，不是原始对象
```

**关键配置：**
```java
@EnableTransactionManagement(proxyTargetClass = true)  // 强制使用CGLIB代理
@AutoConfiguration
@MapperScan({"com.hengjian.**.mapper","com.zsmall.**.mapper"})
public class MybatisPlusConfig {
    // 配置事务管理
}
```

### 2. 代理对象拦截方法调用

```mermaid
sequenceDiagram
    participant Controller
    participant Proxy as 代理对象
    participant TxManager as 事务管理器
    participant DB as 数据库
    participant Original as 原始Service

    Controller->>Proxy: addDistributorProductActive()
    Proxy->>TxManager: 开启事务
    TxManager->>DB: 获取连接，setAutoCommit(false)
    Proxy->>Original: 调用真正的业务方法
    Original->>DB: 执行SQL操作
    Original-->>Proxy: 方法执行完成/异常
    alt 方法正常执行
        Proxy->>TxManager: 提交事务
        TxManager->>DB: commit()
    else 方法抛出异常
        Proxy->>TxManager: 回滚事务
        TxManager->>DB: rollback()
    end
    TxManager->>DB: 释放连接
    Proxy-->>Controller: 返回结果/异常
```

## 🏗️ 技术实现细节

### 1. 代理对象创建

```java
// 代理创建过程（简化版）
public class TransactionProxyFactory {
    public Object createProxy(Object target) {
        // 使用CGLIB创建子类代理
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(target.getClass());
        enhancer.setCallback(new TransactionInterceptor());
        return enhancer.create();
    }
}
```

**代理类型选择：**
- **JDK动态代理**：目标类实现了接口时使用
- **CGLIB代理**：目标类没有实现接口时使用（创建子类代理）
- 配置中的 `proxyTargetClass = true` 强制使用CGLIB代理

### 2. 事务拦截器工作原理

```java
public class TransactionInterceptor implements MethodInterceptor {
    
    @Override
    public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy) {
        // 1. 检查方法是否有@Transactional注解
        Transactional txAnnotation = method.getAnnotation(Transactional.class);
        if (txAnnotation == null) {
            // 没有注解，直接调用原方法
            return proxy.invokeSuper(obj, args);
        }
        
        // 2. 开启事务
        TransactionStatus status = transactionManager.getTransaction(txDefinition);
        
        try {
            // 3. 调用原始方法
            Object result = proxy.invokeSuper(obj, args);
            
            // 4. 提交事务
            transactionManager.commit(status);
            return result;
            
        } catch (RuntimeException | Error e) {
            // 5. 回滚事务
            transactionManager.rollback(status);
            throw e;
        }
    }
}
```

### 3. 数据库连接管理

```java
public class DataSourceTransactionManager {
    
    public TransactionStatus getTransaction(TransactionDefinition definition) {
        // 1. 从连接池获取连接
        Connection connection = dataSource.getConnection();
        
        // 2. 关闭自动提交
        connection.setAutoCommit(false);
        
        // 3. 绑定到当前线程
        TransactionSynchronizationManager.bindResource(dataSource, connection);
        
        return new DefaultTransactionStatus(connection);
    }
    
    public void commit(TransactionStatus status) {
        Connection connection = status.getConnection();
        connection.commit();  // 提交事务
        releaseConnection(connection);
    }
    
    public void rollback(TransactionStatus status) {
        Connection connection = status.getConnection();
        connection.rollback();  // 回滚事务
        releaseConnection(connection);
    }
}
```

## 🔍 ThreadLocal的关键作用

ThreadLocal确保同一个线程中的所有数据库操作使用同一个连接，这是事务隔离的基础：

```java
public class TransactionSynchronizationManager {
    // 每个线程维护自己的事务资源
    private static final ThreadLocal<Map<Object, Object>> resources = 
        new NamedThreadLocal<>("Transactional resources");
    
    // 绑定数据库连接到当前线程
    public static void bindResource(Object key, Object value) {
        Map<Object, Object> map = resources.get();
        if (map == null) {
            map = new HashMap<>();
            resources.set(map);
        }
        map.put(key, value);
    }
    
    // MyBatis等框架从这里获取同一个连接
    public static Object getResource(Object key) {
        Map<Object, Object> map = resources.get();
        return map != null ? map.get(key) : null;
    }
}
```

## 📋 完整执行流程

1. **方法调用阶段**：
   ```
   Controller调用 → 代理对象.addDistributorProductActive()
   ```

2. **事务开启阶段**：
   - 代理对象的拦截器检测到@Transactional注解
   - 调用事务管理器的getTransaction()方法
   - 从连接池获取数据库连接
   - 设置connection.setAutoCommit(false)
   - 将连接绑定到当前线程的ThreadLocal中

3. **业务方法执行阶段**：
   - 调用真正的业务方法
   - 业务方法中的所有数据库操作都使用同一个连接
   - MyBatis等ORM框架会从ThreadLocal中获取这个连接

4. **事务结束阶段**：
   - 如果方法正常执行完成：调用connection.commit()
   - 如果抛出RuntimeException：调用connection.rollback()
   - 清理ThreadLocal中的连接绑定
   - 将连接返回连接池

## ⚠️ 事务失效场景详解

### 1. 异步调用导致事务失效 ⭐⭐⭐⭐⭐

**实际代码示例（存在问题）**：
```java
@Transactional
public void addDistributorProductActive(@Valid DistributorProductActivityAddDTO dto) {
    // 事务范围内的数据库操作
    distributorProductActivityService.save(pa);

    // ❌ 异步操作不在事务范围内！
    ThreadUtil.execAsync(()->{
        // 这里的操作不会参与事务
        rabbitTemplate.convertAndSend(...);
    });
}
```

**原因**：异步线程使用不同的ThreadLocal，无法获取到当前事务的数据库连接。

**解决方案**：
```java
@Transactional
public void addDistributorProductActive(@Valid DistributorProductActivityAddDTO dto) {
    // 先完成所有事务内操作
    distributorProductActivityService.save(pa);

    // 方案1：事务提交后执行异步操作
    TransactionSynchronizationManager.registerSynchronization(
        new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                ThreadUtil.execAsync(() -> {
                    rabbitTemplate.convertAndSend(...);
                });
            }
        }
    );
}
```

### 2. 内部方法调用失效 ⭐⭐⭐⭐⭐

```java
@Service
public class ProductActiveService {

    @Transactional
    public void methodA() {
        // 一些操作

        // ❌ 内部调用，不会经过代理，事务注解失效
        this.methodB();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void methodB() {
        // 这个方法的事务注解不会生效
    }
}
```

**原因**：`this.methodB()` 调用的是原始对象，不是Spring代理对象。

**解决方案**：
```java
@Service
public class ProductActiveService {

    @Autowired
    private ProductActiveService self; // 注入自己

    @Transactional
    public void methodA() {
        // ✅ 通过注入的代理对象调用
        self.methodB();
    }

    // 或者使用AopContext
    @Transactional
    public void methodA2() {
        // ✅ 获取当前代理对象
        ProductActiveService proxy = (ProductActiveService) AopContext.currentProxy();
        proxy.methodB();
    }
}
```

### 3. 方法访问修饰符问题 ⭐⭐⭐⭐

```java
@Service
public class ProductActiveService {

    // ❌ private方法，代理无法拦截
    @Transactional
    private void privateMethod() {
        // 事务不会生效
    }

    // ❌ protected方法在某些代理模式下可能失效
    @Transactional
    protected void protectedMethod() {
        // 可能不生效
    }

    // ❌ final方法，CGLIB无法重写
    @Transactional
    public final void finalMethod() {
        // 事务不会生效
    }
}
```

**解决方案**：
```java
@Service
public class ProductActiveService {

    // ✅ 使用public方法
    @Transactional
    public void publicMethod() {
        // 事务会正常生效
    }
}
```

### 4. 异常类型导致事务不回滚 ⭐⭐⭐⭐

```java
@Service
public class ProductActiveService {

    @Transactional
    public void methodWithCheckedException() throws Exception {
        // 数据库操作
        distributorProductActivityService.save(activity);

        // ❌ 检查异常默认不会回滚事务
        throw new Exception("业务异常");
    }
}
```

**原因**：Spring默认只对RuntimeException和Error进行事务回滚。

**解决方案**：
```java
@Service
public class ProductActiveService {

    // 方案1：指定回滚的异常类型
    @Transactional(rollbackFor = Exception.class)
    public void methodWithCheckedException() throws Exception {
        distributorProductActivityService.save(activity);
        throw new Exception("业务异常"); // ✅ 现在会回滚
    }

    // 方案2：抛出RuntimeException
    @Transactional
    public void methodWithRuntimeException() {
        distributorProductActivityService.save(activity);
        throw new RuntimeException("业务异常"); // ✅ 会回滚
    }
}
```

### 5. 异常被捕获不再抛出 ⭐⭐⭐⭐

```java
@Service
public class ProductActiveService {

    @Transactional
    public void methodCatchException() {
        try {
            distributorProductActivityService.save(activity);
            // 可能抛出异常的操作
            riskyOperation();
        } catch (Exception e) {
            // ❌ 异常被捕获，事务不会回滚
            log.error("操作失败", e);
            // 没有重新抛出异常
        }
    }
}
```

**解决方案**：
```java
@Service
public class ProductActiveService {

    @Transactional
    public void methodCatchException() {
        try {
            distributorProductActivityService.save(activity);
            riskyOperation();
        } catch (Exception e) {
            log.error("操作失败", e);
            // ✅ 重新抛出异常，触发事务回滚
            throw new RuntimeException("操作失败", e);
        }
    }

    // 或者手动标记回滚
    @Transactional
    public void methodManualRollback() {
        try {
            distributorProductActivityService.save(activity);
            riskyOperation();
        } catch (Exception e) {
            log.error("操作失败", e);
            // ✅ 手动标记事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }
}
```

### 6. 数据库引擎不支持事务 ⭐⭐⭐

```sql
-- ❌ MyISAM引擎不支持事务
CREATE TABLE test_table (
    id INT PRIMARY KEY,
    name VARCHAR(50)
) ENGINE=MyISAM;

-- ✅ InnoDB引擎支持事务
CREATE TABLE test_table (
    id INT PRIMARY KEY,
    name VARCHAR(50)
) ENGINE=InnoDB;
```

### 7. 事务传播行为配置错误 ⭐⭐⭐

```java
@Service
public class ProductActiveService {

    @Transactional
    public void outerMethod() {
        // 外层事务
        innerMethod();
    }

    // ❌ NEVER传播行为，如果存在事务会抛异常
    @Transactional(propagation = Propagation.NEVER)
    public void innerMethod() {
        // 这里会抛异常，因为外层已有事务
    }
}
```

### 8. 多数据源配置问题 ⭐⭐⭐

```java
@Service
public class ProductActiveService {

    // ❌ 没有指定事务管理器
    @Transactional
    public void multiDataSourceMethod() {
        // 操作数据源1
        dataSource1Operation();
        // 操作数据源2
        dataSource2Operation();
    }
}
```

**解决方案**：
```java
@Service
public class ProductActiveService {

    // ✅ 指定事务管理器
    @Transactional(transactionManager = "dataSource1TransactionManager")
    public void dataSource1Method() {
        dataSource1Operation();
    }

    @Transactional(transactionManager = "dataSource2TransactionManager")
    public void dataSource2Method() {
        dataSource2Operation();
    }
}
```

### 9. Bean没有被Spring管理 ⭐⭐⭐

```java
// ❌ 没有@Service等注解，不被Spring管理
public class ProductActiveService {

    @Transactional
    public void method() {
        // 事务不会生效
    }
}

// ❌ 手动new的对象
ProductActiveService service = new ProductActiveService();
service.method(); // 事务不会生效
```

**解决方案**：
```java
// ✅ 使用@Service注解
@Service
public class ProductActiveService {

    @Transactional
    public void method() {
        // 事务会生效
    }
}

// ✅ 通过Spring容器获取Bean
@Autowired
private ProductActiveService productActiveService;
```

## 🔍 如何检测事务是否生效

### 1. 日志检测
```yaml
# application.yml
logging:
  level:
    org.springframework.transaction: DEBUG
    org.springframework.orm.jpa: DEBUG
```

### 2. 代码检测
```java
@Transactional
public void testMethod() {
    // 检测是否在事务中
    boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
    log.info("事务是否激活: {}", isTransactionActive);

    // 获取当前事务名称
    String transactionName = TransactionSynchronizationManager.getCurrentTransactionName();
    log.info("当前事务名称: {}", transactionName);
}
```

### 3. 数据库检测
```java
@Transactional
public void testRollback() {
    // 插入数据
    distributorProductActivityService.save(activity);

    // 手动抛出异常测试回滚
    if (true) {
        throw new RuntimeException("测试回滚");
    }
}
```

## 📋 最佳实践建议

1. **避免在@Transactional方法中使用异步操作**
2. **内部方法调用使用代理对象**
3. **明确指定rollbackFor属性**
4. **合理使用事务传播行为**
5. **确保Bean被Spring管理**
6. **使用public方法**
7. **不要捕获异常而不重新抛出**
8. **选择支持事务的数据库引擎**
9. **多数据源时明确指定事务管理器**
10. **定期检查事务是否按预期工作**

## 🎯 总结

`@Transactional` 注解之所以能产生事务效果，核心机制包括：

1. **Spring AOP** 为带有该注解的Bean创建代理对象
2. **代理对象** 拦截方法调用，在方法执行前后添加事务逻辑
3. **事务管理器** 负责开启、提交、回滚事务
4. **ThreadLocal** 确保同一线程中的所有数据库操作使用同一个连接
5. **数据库连接** 设置为手动提交模式，实现事务控制

这种设计模式的优势：
- **声明式**：通过注解声明，无需编写事务管理代码
- **非侵入性**：业务代码与事务管理代码分离
- **灵活配置**：支持多种事务传播行为和隔离级别
- **统一管理**：Spring统一管理不同数据源的事务

理解这些原理和失效场景有助于：
- 正确使用@Transactional注解
- 避免常见的事务失效问题
- 进行事务相关的问题排查
- 优化事务性能
- 提高代码的可靠性和数据一致性

---

*本文档基于Spring Framework事务管理机制编写，涵盖了事务原理、失效场景和最佳实践，适用于Spring Boot项目中的事务管理理解和应用。*
